package utils

const (
	DATE_LAYOUT      = "2006-01-02"
	TIME_LAYOUT      = "15:04:05"
	DATE_TIME_LAYOUT = "2006-01-02 15:04:05"

	// 订单推送子龙失败重推
	QueueZlOrderBinLog = "dc-sz-customer-center-zl-order-binlog"

	MQExchange = "customercenter"
)

var (
	//子龙线下支付类型
	PaymentType = map[int32]struct{}{
		1:    {},
		2:    {},
		3:    {},
		4:    {},
		5:    {},
		6:    {},
		7:    {},
		8:    {},
		9:    {},
		99:   {},
		11:   {},
		12:   {},
		13:   {},
		14:   {},
		15:   {},
		16:   {},
		17:   {},
		18:   {},
		19:   {},
		2004: {},
	}
)
