package services

import (
	"_/models"
	"_/utils"
	"github.com/golang/protobuf/proto"
	"github.com/limitedlee/microservice/common/config"
	"github.com/maybgit/glog"
	"github.com/spf13/cast"
	kit "github.com/tricobbler/rp-kit"
	"github.com/withlin/canal-go/client"
	pbe "github.com/withlin/canal-go/protocol/entry"
	"time"
)

func SubscribeZl() {

	/*
	 与canal服务建立链接
	 ************** : conf目录下 services.properties 中 services.ip = ************ 配置的值
	 example : conf目录下 services.properties 中 services.destinations=example 配置的值
	*/
	address := config.GetString("canal_address")
	port := cast.ToInt(config.GetString("canal_port"))
	destination := config.GetString("canal_zl_destination")
	connector := client.NewSimpleCanalConnector(address, port, "", "", destination, 60000, 60*60*1000)
	err := connector.Connect()
	if err != nil {
		glog.Error("binlog连接canal异常：" + err.Error())
	}

	glog.Info("zl—canal已启动")

	/*
	 mysql 数据解析关注的表，Perl正则表达式.
	 多个正则之间以逗号(,)分隔，转义符需要双斜杠(\\)
	 常见例子：
	 1.  所有表：.*   or  .*\\..*
	 2.  services schema下所有表： services\\..*
	 3.  canal下的以canal打头的表：services\\.services.*
	 4.  services schema下的一张表：services\\.test1
	 5.  多个规则组合使用：services\\..*,mysql.test1,mysql.test2 (逗号分隔)
	*/

	err = connector.Subscribe("hospital_db.pos_payment_method,hospital_db.pos_rma_master")
	if err != nil {
		glog.Error("binlog订阅表异常：" + err.Error())
	}


	ch := make(chan pbe.Entry, 10)

	go SubscribeData(ch)

	for {
		message, err := connector.Get(100, nil, nil)
		if err != nil {
			glog.Error("binlog取数据异常：" + err.Error())
		}

		if message.Id == -1 || len(message.Entries) <= 0 {
			time.Sleep(3 * time.Second)
			continue
		}

		for _, entry := range message.Entries {
			// 事务开始结束不做处理
			if entry.GetEntryType() == pbe.EntryType_TRANSACTIONBEGIN || entry.GetEntryType() == pbe.EntryType_TRANSACTIONEND {
				continue
			}

			// 将entry传给 SubscribeData
			ch <- entry
		}
	}
}

// 订阅数据
func SubscribeData(ch <-chan pbe.Entry) {

	for entry := range ch {
		rowChange := new(pbe.RowChange)
		err := proto.Unmarshal(entry.GetStoreValue(), rowChange)
		if err != nil {
			continue
		}

		for _, row := range rowChange.GetRowDatas() {
			switch rowChange.GetEventType() {
			// 暂时只考虑插入数据
			case pbe.EventType_INSERT:
				data := make(map[string]string)
				tableName := entry.GetHeader().GetTableName()
				for _, filed := range row.GetAfterColumns() {
					data[filed.Name] = filed.Value
				}
				dataType := int32(0)
				var id int
				if tableName == "pos_payment_method" {
					if len(data["id"]) > 0 {
						dataType = 1
						if _, ok := utils.PaymentType[cast.ToInt32(data["payment_type"])]; ok {
							id = cast.ToInt(data["id"])
						}
					}

				}
				//else if tableName == "pos_rma_master" {
				//	if len(data["id"]) > 0 {
				//		dataType = 2
				//		if cast.ToInt(data["rma_type"]) != 3{
				//			id = cast.ToInt(data["id"])
				//		}
				//	}
				//}
				if id >0 {
					request := models.MqZlOrderBinLogTask{
						Type:       dataType,
						Id: id,
					}
					requestJson := kit.JsonEncode(request)
					glog.Info("createZlOrderBinLogTask:", requestJson)
					if ok := utils.PublishRabbitMQ(utils.QueueZlOrderBinLog, requestJson, utils.MQExchange); !ok {
					}
				}

				// update
				//case pbe.EventType_UPDATE:
				//// 根据主键更新
				//valueSetExpression := make([]string,0)
				//var conditionExpression string
				//for _, filed := range row.GetAfterColumns() {
				//	if filed.IsKey{
				//		conditionExpression = filed.Name + "=" +  "'" + filed.Value + "'"
				//	}
				//	if filed.Updated {
				//		valueSetExpression = append(valueSetExpression, filed.Name + "=" + "'" + filed.Value + "'")
				//	}
				//}
				//valueSetExpressionString := strings.Join(valueSetExpression,",")
				//sql := "update %s set %s where %s"
				//_ , err := db.Exec(fmt.Sprintf(sql,entry.GetHeader().GetTableName() , valueSetExpressionString, conditionExpression))
				//if err != nil {
				//	fmt.Printf("db error <%v>",err)
				//}
				// delete
				//case pbe.EventType_DELETE:
				// 根据主键删除
				//var conditionExpression string
				//for _, filed := range row.GetBeforeColumns() {
				//	if filed.IsKey{
				//		conditionExpression = filed.Name + "=" + "'" + filed.Value + "'"
				//	}
				//}
				//sql := "delete from %s where %s"
				//_ , err := db.Exec(fmt.Sprintf(sql,entry.GetHeader().GetTableName() , conditionExpression))
				//if err != nil {
				//	fmt.Printf("db error <%v>",err)
				//}
			}
		}
	}
}
